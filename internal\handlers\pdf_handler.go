package handlers

import (
	"fmt"
	"net/http"
	"path/filepath"
	"time"

	"Pdf_Generator/internal/models"
	"Pdf_Generator/internal/pdf"

	"github.com/gin-gonic/gin"
)

func GeneratePDFHandler(c *gin.Context) {
	var req models.PDFRequest

	// Bind incoming JSON to struct
	if err := c.ShouldBindJSON(&req); err != nil {
		c.J<PERSON>(http.StatusBadRequest, gin.H{"error": "Invalid input: " + err.Error()})
		return
	}

	// Generate unique filename with timestamp
	timestamp := time.Now().Format("20060102_150405")
	fileName := fmt.Sprintf("itinerary_%s.pdf", timestamp)
	outputPath := filepath.Join("pdf", fileName) // Saves in ./pdf/

	// Call PDF generation logic
	err := pdf.GeneratePDFWithOverlay(&req, outputPath)
	if err != nil {
		c.<PERSON>(http.StatusInternalServerError, gin.H{"error": "Failed to generate PDF", "details": err.Error()})
		return
	}

	// Return file path or success message
	c.J<PERSON>(http.StatusOK, gin.H{
		"message":   "PDF successfully generated",
		"file_path": outputPath,
	})
}
