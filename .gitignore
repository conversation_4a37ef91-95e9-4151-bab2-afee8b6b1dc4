# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib

# Test binary, built with "go test -c"
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Dependency directories (remove the comment below to include it)
# vendor/

# Go workspace file
go.work
tmp/

# IDE specific files
.vscode
.idea

# .env file
.env

# Project build
main
*templ.go

# OS X generated file
.DS_Store

